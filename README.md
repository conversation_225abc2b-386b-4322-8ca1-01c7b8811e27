# 奥图威智能企业网站

基于Vue 3、Vite和Tailwind CSS构建的现代工业自动化企业网站。

## 技术栈

- Vue 3 - 渐进式JavaScript框架
- Vite - 新一代前端构建工具
- Tailwind CSS - 实用优先的CSS框架
- Font Awesome - 图标库

## 功能特点

- 响应式设计，适配各种设备屏幕尺寸
- 基于组件的架构，易于维护和扩展
- 动画和过渡效果增强用户体验
- 优化的性能和加载速度

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式运行

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 🚀 阿里云 ECS 部署

### 一键部署
```bash
# 设置执行权限
chmod +x deploy.sh

# 执行部署（替换为你的 ECS IP）
./deploy.sh 47.96.123.456 root
```

### 快速更新
```bash
# 更新网站内容
./update.sh 47.96.123.456 root
```

### 检查部署状态
```bash
# 检查服务器状态
./check-deployment.sh 47.96.123.456 root
```

详细部署说明请查看 [DEPLOYMENT.md](./DEPLOYMENT.md)

## 项目结构

```
atv-website/
├── public/          # 静态资源
├── src/             # 源代码
│   ├── assets/      # 项目资源文件
│   ├── components/  # Vue组件
│   │   ├── layout/  # 布局组件
│   │   └── sections/# 页面部分组件
│   ├── directives/  # Vue自定义指令
│   ├── App.vue      # 主应用组件
│   ├── main.js      # 应用入口
│   └── index.css    # 全局样式
├── index.html       # HTML模板
├── package.json     # 项目依赖
├── tailwind.config.js # Tailwind配置
└── vite.config.js   # Vite配置
```

## 自定义配置

请参阅 [Vite配置参考](https://vitejs.dev/config/)。

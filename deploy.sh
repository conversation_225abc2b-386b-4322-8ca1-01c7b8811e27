#!/bin/bash

# 阿里云 ECS 部署脚本
# 使用方法: ./deploy.sh [ECS_IP] [USERNAME]
# 示例: ./deploy.sh 47.96.123.456 root

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
ECS_IP=${1:-"your-ecs-ip"}
USERNAME=${2:-"root"}
PROJECT_NAME="atv-website"
REMOTE_DIR="/var/www/${PROJECT_NAME}"
NGINX_CONF="/etc/nginx/conf.d/${PROJECT_NAME}.conf"

echo -e "${BLUE}🚀 开始部署 ${PROJECT_NAME} 到阿里云 ECS...${NC}"

# 检查参数
if [ "$ECS_IP" = "your-ecs-ip" ]; then
    echo -e "${RED}❌ 请提供 ECS IP 地址${NC}"
    echo -e "${YELLOW}使用方法: ./deploy.sh [ECS_IP] [USERNAME]${NC}"
    exit 1
fi

# 1. 本地构建
echo -e "${YELLOW}📦 正在构建项目...${NC}"
npm run build

# 2. 创建部署包
echo -e "${YELLOW}📁 创建部署包...${NC}"
tar -czf ${PROJECT_NAME}-dist.tar.gz -C dist .

# 3. 上传文件到服务器
echo -e "${YELLOW}📤 上传文件到服务器...${NC}"
scp ${PROJECT_NAME}-dist.tar.gz ${USERNAME}@${ECS_IP}:/tmp/
scp nginx.conf ${USERNAME}@${ECS_IP}:/tmp/${PROJECT_NAME}.conf
scp server-setup.sh ${USERNAME}@${ECS_IP}:/tmp/

# 4. 在服务器上执行部署
echo -e "${YELLOW}🔧 在服务器上执行部署...${NC}"
ssh ${USERNAME}@${ECS_IP} "chmod +x /tmp/server-setup.sh && /tmp/server-setup.sh ${PROJECT_NAME}"

# 5. 清理本地临时文件
echo -e "${YELLOW}🧹 清理临时文件...${NC}"
rm -f ${PROJECT_NAME}-dist.tar.gz

echo -e "${GREEN}✅ 部署完成！${NC}"
echo -e "${BLUE}🌐 访问地址: http://${ECS_IP}${NC}"
echo -e "${BLUE}📊 查看 Nginx 状态: ssh ${USERNAME}@${ECS_IP} 'systemctl status nginx'${NC}"

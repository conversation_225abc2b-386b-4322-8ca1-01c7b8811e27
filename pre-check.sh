#!/bin/bash

# ECS 环境预检查脚本
# 在部署前运行此脚本检查服务器环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 ECS 环境预检查开始...${NC}"
echo

# 1. 操作系统检查
echo -e "${BLUE}=== 操作系统信息 ===${NC}"
if [ -f /etc/redhat-release ]; then
    OS_VERSION=$(cat /etc/redhat-release)
    echo -e "操作系统: ${GREEN}${OS_VERSION}${NC}"
    
    if echo "$OS_VERSION" | grep -q "CentOS.*7"; then
        echo -e "版本兼容性: ${GREEN}✅ 支持${NC}"
    else
        echo -e "版本兼容性: ${YELLOW}⚠️  建议使用 CentOS 7.x${NC}"
    fi
else
    echo -e "操作系统: ${RED}❌ 非 CentOS 系统${NC}"
fi

echo "内核版本: $(uname -r)"
echo "架构: $(uname -m)"
echo

# 2. 系统资源检查
echo -e "${BLUE}=== 系统资源 ===${NC}"

# CPU
CPU_CORES=$(nproc)
echo "CPU 核心数: $CPU_CORES"
if [ $CPU_CORES -ge 1 ]; then
    echo -e "CPU 要求: ${GREEN}✅ 满足${NC}"
else
    echo -e "CPU 要求: ${RED}❌ 不足${NC}"
fi

# 内存
MEMORY_GB=$(free -g | awk 'NR==2{print $2}')
echo "内存大小: ${MEMORY_GB}GB"
if [ $MEMORY_GB -ge 2 ]; then
    echo -e "内存要求: ${GREEN}✅ 满足${NC}"
else
    echo -e "内存要求: ${YELLOW}⚠️  建议至少 2GB${NC}"
fi

# 磁盘空间
DISK_AVAIL=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
echo "可用磁盘: ${DISK_AVAIL}GB"
if [ $DISK_AVAIL -ge 10 ]; then
    echo -e "磁盘空间: ${GREEN}✅ 充足${NC}"
else
    echo -e "磁盘空间: ${RED}❌ 不足，建议至少 10GB 可用空间${NC}"
fi
echo

# 3. 网络检查
echo -e "${BLUE}=== 网络连接 ===${NC}"

# 检查公网连接
if ping -c 3 8.8.8.8 >/dev/null 2>&1; then
    echo -e "外网连接: ${GREEN}✅ 正常${NC}"
else
    echo -e "外网连接: ${RED}❌ 无法连接外网${NC}"
fi

# 检查 DNS 解析
if nslookup google.com >/dev/null 2>&1; then
    echo -e "DNS 解析: ${GREEN}✅ 正常${NC}"
else
    echo -e "DNS 解析: ${RED}❌ DNS 解析失败${NC}"
fi

# 检查端口占用
if netstat -tlnp | grep :80 >/dev/null 2>&1; then
    echo -e "端口 80: ${YELLOW}⚠️  已被占用${NC}"
    netstat -tlnp | grep :80
else
    echo -e "端口 80: ${GREEN}✅ 可用${NC}"
fi
echo

# 4. 权限检查
echo -e "${BLUE}=== 权限检查 ===${NC}"
if [ "$EUID" -eq 0 ]; then
    echo -e "管理员权限: ${GREEN}✅ Root 用户${NC}"
elif sudo -n true 2>/dev/null; then
    echo -e "管理员权限: ${GREEN}✅ 有 sudo 权限${NC}"
else
    echo -e "管理员权限: ${RED}❌ 需要 root 或 sudo 权限${NC}"
fi
echo

# 5. 软件包管理器检查
echo -e "${BLUE}=== 软件包管理 ===${NC}"
if command -v yum >/dev/null 2>&1; then
    echo -e "YUM 包管理器: ${GREEN}✅ 可用${NC}"
    
    # 检查 EPEL 仓库
    if yum repolist | grep -q epel; then
        echo -e "EPEL 仓库: ${GREEN}✅ 已安装${NC}"
    else
        echo -e "EPEL 仓库: ${YELLOW}⚠️  未安装（部署时会自动安装）${NC}"
    fi
else
    echo -e "YUM 包管理器: ${RED}❌ 不可用${NC}"
fi
echo

# 6. 防火墙状态
echo -e "${BLUE}=== 防火墙状态 ===${NC}"
if systemctl is-active --quiet firewalld; then
    echo -e "防火墙服务: ${GREEN}✅ 运行中${NC}"
    
    # 检查 HTTP 服务是否开放
    if firewall-cmd --list-services | grep -q http; then
        echo -e "HTTP 端口: ${GREEN}✅ 已开放${NC}"
    else
        echo -e "HTTP 端口: ${YELLOW}⚠️  未开放（部署时会自动配置）${NC}"
    fi
else
    echo -e "防火墙服务: ${YELLOW}⚠️  未运行${NC}"
fi
echo

# 7. 总结
echo -e "${BLUE}=== 检查总结 ===${NC}"
echo -e "${GREEN}✅ 表示满足要求${NC}"
echo -e "${YELLOW}⚠️  表示需要注意但不影响部署${NC}"
echo -e "${RED}❌ 表示需要解决的问题${NC}"
echo
echo -e "${BLUE}💡 如果所有关键项都显示 ✅，可以开始部署！${NC}"
echo -e "${BLUE}🚀 部署命令: ./deploy.sh YOUR_ECS_IP root${NC}"

#!/bin/bash

# 快速更新部署脚本
# 用于已经配置好的服务器快速更新网站内容

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
ECS_IP=${1:-"your-ecs-ip"}
USERNAME=${2:-"root"}
PROJECT_NAME="atv-website"
REMOTE_DIR="/var/www/${PROJECT_NAME}"

echo -e "${BLUE}🔄 快速更新 ${PROJECT_NAME}...${NC}"

# 检查参数
if [ "$ECS_IP" = "your-ecs-ip" ]; then
    echo -e "${RED}❌ 请提供 ECS IP 地址${NC}"
    echo -e "${YELLOW}使用方法: ./update.sh [ECS_IP] [USERNAME]${NC}"
    exit 1
fi

# 1. 本地构建
echo -e "${YELLOW}📦 构建最新版本...${NC}"
npm run build

# 2. 创建备份和更新脚本
cat > quick-update.sh << 'EOF'
#!/bin/bash
PROJECT_NAME="atv-website"
REMOTE_DIR="/var/www/${PROJECT_NAME}"
BACKUP_DIR="/var/backups/${PROJECT_NAME}"

echo "🔄 开始快速更新..."

# 创建备份
mkdir -p ${BACKUP_DIR}
cp -r ${REMOTE_DIR} ${BACKUP_DIR}/backup-$(date +%Y%m%d-%H%M%S)

# 清理旧文件（保留备份）
find ${BACKUP_DIR} -name "backup-*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true

# 解压新文件
cd ${REMOTE_DIR}
tar -xzf /tmp/atv-website-dist.tar.gz

# 设置权限
chown -R nginx:nginx ${REMOTE_DIR}
chmod -R 755 ${REMOTE_DIR}

echo "✅ 更新完成！"
EOF

# 3. 创建部署包
echo -e "${YELLOW}📁 创建部署包...${NC}"
tar -czf ${PROJECT_NAME}-dist.tar.gz -C dist .

# 4. 上传文件
echo -e "${YELLOW}📤 上传更新文件...${NC}"
scp ${PROJECT_NAME}-dist.tar.gz ${USERNAME}@${ECS_IP}:/tmp/
scp quick-update.sh ${USERNAME}@${ECS_IP}:/tmp/

# 5. 执行更新
echo -e "${YELLOW}🚀 执行服务器更新...${NC}"
ssh ${USERNAME}@${ECS_IP} "chmod +x /tmp/quick-update.sh && /tmp/quick-update.sh && rm -f /tmp/quick-update.sh /tmp/${PROJECT_NAME}-dist.tar.gz"

# 6. 清理本地文件
rm -f ${PROJECT_NAME}-dist.tar.gz quick-update.sh

echo -e "${GREEN}✅ 更新完成！${NC}"
echo -e "${BLUE}🌐 访问地址: http://${ECS_IP}${NC}"
echo -e "${BLUE}💡 提示: 可能需要清除浏览器缓存查看最新内容${NC}"

# 🚀 阿里云 ECS 部署指南

## 📋 部署前准备

### 1. ECS 实例要求
- **操作系统**: CentOS 7.9
- **最小配置**: 1核2GB内存，40GB系统盘
- **网络**: 确保安全组开放 80 和 443 端口

### 2. 本地环境要求
- Node.js 16+ 
- npm 或 yarn
- SSH 客户端

## 🔧 快速部署

### 方法一：自动化部署（推荐）

1. **设置脚本权限**
```bash
chmod +x deploy.sh
chmod +x server-setup.sh
```

2. **执行部署**
```bash
# 替换为你的 ECS IP 地址
./deploy.sh 47.96.123.456 root
```

### 方法二：手动部署

#### 步骤 1: 本地构建
```bash
npm install
npm run build
```

#### 步骤 2: 上传文件
```bash
# 创建部署包
tar -czf atv-website-dist.tar.gz -C dist .

# 上传到服务器
scp atv-website-dist.tar.gz root@YOUR_ECS_IP:/tmp/
scp nginx.conf root@YOUR_ECS_IP:/tmp/atv-website.conf
scp server-setup.sh root@YOUR_ECS_IP:/tmp/
```

#### 步骤 3: 服务器配置
```bash
# SSH 连接到服务器
ssh root@YOUR_ECS_IP

# 执行服务器配置脚本
chmod +x /tmp/server-setup.sh
/tmp/server-setup.sh atv-website
```

## 🔍 验证部署

### 1. 检查服务状态
```bash
# 检查 Nginx 状态
systemctl status nginx

# 检查端口监听
netstat -tlnp | grep :80
```

### 2. 测试网站访问
```bash
# 本地测试
curl -I http://YOUR_ECS_IP

# 浏览器访问
http://YOUR_ECS_IP
```

## 📁 目录结构

```
/var/www/atv-website/          # 网站根目录
├── index.html                 # 主页面
├── assets/                    # 静态资源
│   ├── index-*.js            # JavaScript 文件
│   └── index-*.css           # CSS 文件
└── vite.svg                  # 图标文件

/etc/nginx/conf.d/             # Nginx 配置
└── atv-website.conf          # 项目配置文件

/var/log/nginx/atv-website/    # 日志目录
├── access.log                # 访问日志
└── error.log                 # 错误日志
```

## 🛠️ 常用运维命令

### Nginx 管理
```bash
# 启动 Nginx
systemctl start nginx

# 停止 Nginx
systemctl stop nginx

# 重启 Nginx
systemctl restart nginx

# 重新加载配置
systemctl reload nginx

# 测试配置文件
nginx -t
```

### 日志查看
```bash
# 查看访问日志
tail -f /var/log/nginx/atv-website/access.log

# 查看错误日志
tail -f /var/log/nginx/atv-website/error.log

# 查看 Nginx 主日志
tail -f /var/log/nginx/error.log
```

### 更新网站
```bash
# 重新部署（在本地执行）
./deploy.sh YOUR_ECS_IP root
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# 检查防火墙状态
systemctl status firewalld

# 开放 HTTP 端口
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload
```

### 2. SSL 证书配置（可选）
如需 HTTPS，可以使用 Let's Encrypt：
```bash
# 安装 Certbot
yum install -y certbot python2-certbot-nginx

# 获取证书（替换为你的域名）
certbot --nginx -d yourdomain.com
```

## 🐛 故障排除

### 常见问题

1. **502 Bad Gateway**
   - 检查 Nginx 配置文件语法
   - 确认网站文件存在且权限正确

2. **403 Forbidden**
   - 检查文件权限：`chmod -R 755 /var/www/atv-website`
   - 检查 SELinux：`setsebool -P httpd_can_network_connect 1`

3. **无法访问**
   - 检查防火墙设置
   - 确认安全组规则
   - 检查 Nginx 是否运行

### 调试命令
```bash
# 检查 Nginx 进程
ps aux | grep nginx

# 检查端口占用
netstat -tlnp | grep :80

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 📞 技术支持

如遇到问题，请检查：
1. ECS 实例安全组配置
2. 防火墙设置
3. Nginx 配置文件语法
4. 文件权限设置

部署完成后，你的网站将在 `http://YOUR_ECS_IP` 可以访问！

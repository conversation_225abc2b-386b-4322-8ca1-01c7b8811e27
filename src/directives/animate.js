// 滚动动画指令
export const directive = {
  mounted(el) {
    el.classList.add('opacity-0', 'translate-y-10', 'transition-all', 'duration-1000');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            el.classList.remove('opacity-0', 'translate-y-10');
            el.classList.add('opacity-100', 'translate-y-0');
          }, el.dataset.delay ? parseFloat(el.dataset.delay) * 1000 : 0);
          
          observer.unobserve(el);
        }
      });
    }, { threshold: 0.1 });
    
    observer.observe(el);
  }
}; 
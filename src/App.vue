<script setup>
import LoadingScreen from './components/layout/LoadingScreen.vue';
import BackToTop from './components/layout/BackToTop.vue';
import TheHeader from './components/layout/TheHeader.vue';
import TheFooter from './components/layout/TheFooter.vue';
import HeroSection from './components/sections/HeroSection.vue';
import ServicesSection from './components/sections/ServicesSection.vue';
import SolutionsSection from './components/sections/SolutionsSection.vue';
</script>

<template>
  <div class="app">
    <loading-screen />
    <back-to-top />
    
    <the-header />
    
    <main>
      <hero-section />
      <services-section />
      <solutions-section />
    </main>
    
    <the-footer />
  </div>
</template>

<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

.app {
  font-family: 'Poppins', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
</style>

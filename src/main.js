import { createApp } from 'vue'
import App from './App.vue'
import './index.css'
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { 
  faArrowUp, faRobot, faIndustry, faWarehouse, 
  faArrowRight, faExternalLinkAlt, faCheck,
  faLongArrowAltRight, faMapMarkerAlt, faPhone,
  faEnvelope, faClock
} from '@fortawesome/free-solid-svg-icons'
import { directive as vAnimate } from './directives/animate'

// 添加图标到库
library.add(
  faArrowUp, faRobot, faIndustry, faWarehouse, 
  faArrowRight, faExternalLinkAlt, faCheck,
  faLongArrowAltRight, faMapMarkerAlt, faPhone,
  faEnvelope, faClock
)

const app = createApp(App)
app.component('font-awesome-icon', FontAwesomeIcon)
app.directive('animate', vAnimate)
app.mount('#app')

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 md:py-16;
  }
  
  .btn-primary {
    @apply bg-primary text-white font-medium py-3 px-6 rounded-md shadow-md hover:bg-secondary transition duration-300 inline-flex items-center;
  }
  
  .btn-secondary {
    @apply border-2 border-secondary text-secondary font-medium py-3 px-6 rounded-md hover:bg-secondary hover:text-white transition duration-300 inline-flex items-center;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold mb-2;
  }
  
  .section-subtitle {
    @apply text-sm uppercase tracking-wider font-medium text-primary mb-3;
  }
  
  .animate-on-scroll {
    @apply opacity-0 transition-all duration-1000;
  }
  
  .animate-on-scroll.animated {
    @apply opacity-100;
  }
} 
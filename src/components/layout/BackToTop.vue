<template>
  <button 
    aria-label="返回顶部" 
    @click="scrollToTop"
    class="fixed bottom-6 right-6 w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center shadow-lg transition-all duration-300 z-40"
    :class="{ 'translate-y-20': !showButton, 'hover:bg-secondary': showButton }"
  >
    <font-awesome-icon :icon="['fas', 'arrow-up']" />
  </button>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const showButton = ref(false);

const handleScroll = () => {
  showButton.value = window.scrollY > 300;
};

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script> 
<template>
  <div 
    class="fixed inset-0 bg-white flex flex-col items-center justify-center z-50 transition-opacity duration-500"
    :class="{ 'opacity-0 pointer-events-none': !isLoading }"
  >
    <div class="w-12 h-12 border-4 border-primary border-b-secondary border-t-transparent rounded-full animate-spin"></div>
    <p class="mt-4 text-tech-gray">加载中...</p>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const isLoading = ref(true);

onMounted(() => {
  setTimeout(() => {
    isLoading.value = false;
  }, 1000);
});
</script> 
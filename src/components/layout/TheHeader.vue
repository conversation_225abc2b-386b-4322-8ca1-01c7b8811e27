<template>
  <header :class="[
    'fixed w-full top-0 z-40 transition-all duration-300', 
    { 'bg-white shadow-md py-2': scrolled, 'py-4': !scrolled }
  ]">
    <div class="container flex justify-between items-center">
      <a href="#home" class="text-2xl font-bold text-primary transition-colors hover:text-secondary">奥图威智能</a>
      
      <nav class="hidden lg:block">
        <ul class="flex space-x-8">
          <li><a href="#home" class="nav-link">首页</a></li>
          <li><a href="#services" class="nav-link">服务</a></li>
          <li><a href="#solutions" class="nav-link">解决方案</a></li>
          <li><a href="#" class="nav-link">联系我们</a></li>
        </ul>
      </nav>
      
      <button 
        @click="toggleMenu" 
        class="lg:hidden w-8 h-6 flex flex-col justify-between"
        :aria-expanded="mobileMenuOpen"
        aria-label="菜单"
      >
        <span 
          class="w-full h-0.5 bg-tech-gray transition-all duration-300"
          :class="{ 'transform rotate-45 translate-y-2.5': mobileMenuOpen }"
        ></span>
        <span 
          class="w-full h-0.5 bg-tech-gray transition-all duration-300"
          :class="{ 'opacity-0': mobileMenuOpen }"
        ></span>
        <span 
          class="w-full h-0.5 bg-tech-gray transition-all duration-300"
          :class="{ 'transform -rotate-45 -translate-y-2.5': mobileMenuOpen }"
        ></span>
      </button>
      
      <!-- 移动菜单 -->
      <div 
        class="fixed inset-0 bg-white pt-20 px-6 z-30 transform transition-transform duration-300 lg:hidden"
        :class="{ 'translate-x-0': mobileMenuOpen, 'translate-x-full': !mobileMenuOpen }"
      >
        <ul class="flex flex-col space-y-6">
          <li>
            <a 
              @click="closeMenu" 
              href="#home" 
              class="text-xl font-medium block py-2 border-b border-gray-100 text-tech-gray hover:text-primary transition-colors"
            >首页</a>
          </li>
          <li>
            <a 
              @click="closeMenu" 
              href="#services" 
              class="text-xl font-medium block py-2 border-b border-gray-100 text-tech-gray hover:text-primary transition-colors"
            >服务</a>
          </li>
          <li>
            <a 
              @click="closeMenu" 
              href="#solutions" 
              class="text-xl font-medium block py-2 border-b border-gray-100 text-tech-gray hover:text-primary transition-colors"
            >解决方案</a>
          </li>
          <li>
            <a 
              @click="closeMenu" 
              href="#" 
              class="text-xl font-medium block py-2 border-b border-gray-100 text-tech-gray hover:text-primary transition-colors"
            >联系我们</a>
          </li>
        </ul>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const scrolled = ref(false);
const mobileMenuOpen = ref(false);

const handleScroll = () => {
  scrolled.value = window.scrollY > 50;
};

const toggleMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
  if (mobileMenuOpen.value) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
};

const closeMenu = () => {
  mobileMenuOpen.value = false;
  document.body.style.overflow = '';
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  handleScroll(); // 初始检查
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped>
.nav-link {
  @apply relative text-tech-gray font-medium transition-colors hover:text-primary;
}

.nav-link::after {
  content: '';
  @apply absolute left-0 bottom-[-4px] w-0 h-0.5 bg-primary transition-all duration-300;
}

.nav-link:hover::after {
  @apply w-full;
}
</style> 
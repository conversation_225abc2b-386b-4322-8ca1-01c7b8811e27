<template>
  <section id="solutions" class="section-padding bg-gray-50">
    <div class="container">
      <div class="text-center mb-10">
        <p class="section-subtitle" v-animate>创新技术</p>
        <h2 class="section-title text-tech-gray" v-animate>我们的<span class="text-primary">解决方案</span></h2>
        <div class="w-24 h-1 bg-primary mx-auto my-4" v-animate></div>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto" v-animate>面向现代工业的尖端自动化解决方案</p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 解决方案卡片1 -->
        <div 
          class="bg-white rounded-lg overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl group"
          v-animate
          data-delay="0.1"
        >
          <div class="relative h-52 bg-gradient-to-br from-secondary to-secondary/60 overflow-hidden flex items-center justify-center">
            <font-awesome-icon 
              :icon="['fas', 'industry']" 
              class="text-6xl text-white opacity-50"
            />
            <div class="absolute inset-0 bg-tech-gray bg-opacity-80 flex items-center justify-center opacity-0 transition-opacity group-hover:opacity-100">
              <a href="#" class="text-white font-medium flex items-center">
                查看项目 
                <font-awesome-icon :icon="['fas', 'external-link-alt']" class="ml-2" />
              </a>
            </div>
          </div>
          
          <div class="p-6">
            <div class="text-xs font-semibold text-secondary uppercase tracking-wider mb-2">智能制造</div>
            <h3 class="text-xl font-bold mb-3 text-tech-gray">智能工厂解决方案</h3>
            <p class="text-gray-600 mb-4">利用物联网、人工智能和机器学习的智能制造系统，实现工业4.0转型。</p>
            
            <ul class="space-y-2 mb-5">
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>预测性维护</span>
              </li>
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>实时监控</span>
              </li>
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>数字孪生技术</span>
              </li>
            </ul>
            
            <a href="#" class="text-secondary font-medium inline-flex items-center group-hover:text-primary transition-colors">
              探索更多 
              <font-awesome-icon :icon="['fas', 'long-arrow-alt-right']" class="ml-2 transition-transform duration-300 group-hover:translate-x-2" />
            </a>
          </div>
        </div>
        
        <!-- 解决方案卡片2 -->
        <div 
          class="bg-white rounded-lg overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl group"
          v-animate
          data-delay="0.2"
        >
          <div class="relative h-52 bg-gradient-to-br from-secondary to-secondary/60 overflow-hidden flex items-center justify-center">
            <font-awesome-icon 
              :icon="['fas', 'robot']" 
              class="text-6xl text-white opacity-50"
            />
            <div class="absolute inset-0 bg-tech-gray bg-opacity-80 flex items-center justify-center opacity-0 transition-opacity group-hover:opacity-100">
              <a href="#" class="text-white font-medium flex items-center">
                查看项目 
                <font-awesome-icon :icon="['fas', 'external-link-alt']" class="ml-2" />
              </a>
            </div>
          </div>
          
          <div class="p-6">
            <div class="text-xs font-semibold text-secondary uppercase tracking-wider mb-2">先进机器人</div>
            <h3 class="text-xl font-bold mb-3 text-tech-gray">工业机器人</h3>
            <p class="text-gray-600 mb-4">专为制造和物流领域设计的高精度、高效、灵活的先进机器人系统。</p>
            
            <ul class="space-y-2 mb-5">
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>协作机器人</span>
              </li>
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>自动导引车</span>
              </li>
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>机器人流程自动化</span>
              </li>
            </ul>
            
            <a href="#" class="text-secondary font-medium inline-flex items-center group-hover:text-primary transition-colors">
              探索更多 
              <font-awesome-icon :icon="['fas', 'long-arrow-alt-right']" class="ml-2 transition-transform duration-300 group-hover:translate-x-2" />
            </a>
          </div>
        </div>
        
        <!-- 解决方案卡片3 -->
        <div 
          class="bg-white rounded-lg overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl group"
          v-animate
          data-delay="0.3"
        >
          <div class="relative h-52 bg-gradient-to-br from-secondary to-secondary/60 overflow-hidden flex items-center justify-center">
            <font-awesome-icon 
              :icon="['fas', 'warehouse']" 
              class="text-6xl text-white opacity-50"
            />
            <div class="absolute inset-0 bg-tech-gray bg-opacity-80 flex items-center justify-center opacity-0 transition-opacity group-hover:opacity-100">
              <a href="#" class="text-white font-medium flex items-center">
                查看项目 
                <font-awesome-icon :icon="['fas', 'external-link-alt']" class="ml-2" />
              </a>
            </div>
          </div>
          
          <div class="p-6">
            <div class="text-xs font-semibold text-secondary uppercase tracking-wider mb-2">仓库管理</div>
            <h3 class="text-xl font-bold mb-3 text-tech-gray">仓库自动化</h3>
            <p class="text-gray-600 mb-4">端到端仓库管理解决方案，优化库存并简化操作流程。</p>
            
            <ul class="space-y-2 mb-5">
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>自动化存取系统</span>
              </li>
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>库存管理</span>
              </li>
              <li class="flex items-center">
                <font-awesome-icon :icon="['fas', 'check']" class="text-secondary mr-2" />
                <span>订单履行</span>
              </li>
            </ul>
            
            <a href="#" class="text-secondary font-medium inline-flex items-center group-hover:text-primary transition-colors">
              探索更多 
              <font-awesome-icon :icon="['fas', 'long-arrow-alt-right']" class="ml-2 transition-transform duration-300 group-hover:translate-x-2" />
            </a>
          </div>
        </div>
      </div>
      
      <div class="text-center mt-10" v-animate data-delay="0.4">
        <a href="#" class="btn-primary">
          查看全部解决方案
          <font-awesome-icon :icon="['fas', 'arrow-right']" class="ml-2" />
        </a>
      </div>
    </div>
  </section>
</template>

<script setup>
import { directive as vAnimate } from '../../directives/animate';
</script> 
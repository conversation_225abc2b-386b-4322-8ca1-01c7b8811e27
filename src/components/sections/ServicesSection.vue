<template>
  <section id="services" class="section-padding bg-white">
    <div class="container">
      <div class="text-center mb-10">
        <p class="section-subtitle" v-animate>我们的服务</p>
        <h2 class="section-title text-tech-gray" v-animate>我们的<span class="text-primary">服务</span></h2>
        <div class="w-24 h-1 bg-primary mx-auto my-4" v-animate></div>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto" v-animate>根据您的业务需求定制的全面自动化解决方案</p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- 服务卡片1 -->
        <div 
          class="bg-white rounded-lg p-8 shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group"
          v-animate
          data-delay="0.1"
        >
          <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6 group-hover:bg-primary transition-colors duration-300">
            <font-awesome-icon 
              :icon="['fas', 'robot']" 
              class="text-2xl text-primary group-hover:text-white transition-colors duration-300"
            />
          </div>
          <h3 class="text-xl font-bold mb-4 text-tech-gray">机器人开发</h3>
          <p class="text-gray-600 mb-6">定制机器人解决方案，利用尖端技术提升制造、物流和工业运营效率。</p>
          <a href="#" class="text-primary font-medium inline-flex items-center group-hover:text-tech-gray transition-colors">
            了解更多 
            <font-awesome-icon :icon="['fas', 'arrow-right']" class="ml-2 transition-transform duration-300 group-hover:translate-x-2" />
          </a>
        </div>
        
        <!-- 服务卡片2 -->
        <div 
          class="bg-white rounded-lg p-8 shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group"
          v-animate
          data-delay="0.2"
        >
          <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6 group-hover:bg-primary transition-colors duration-300">
            <font-awesome-icon 
              :icon="['fas', 'industry']" 
              class="text-2xl text-primary group-hover:text-white transition-colors duration-300"
            />
          </div>
          <h3 class="text-xl font-bold mb-4 text-tech-gray">工业自动化</h3>
          <p class="text-gray-600 mb-6">端到端自动化系统，提高效率，降低成本，最大化运营生产力。</p>
          <a href="#" class="text-primary font-medium inline-flex items-center group-hover:text-tech-gray transition-colors">
            了解更多 
            <font-awesome-icon :icon="['fas', 'arrow-right']" class="ml-2 transition-transform duration-300 group-hover:translate-x-2" />
          </a>
        </div>
        
        <!-- 服务卡片3 -->
        <div 
          class="bg-white rounded-lg p-8 shadow-lg border border-gray-200 transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group"
          v-animate
          data-delay="0.3"
        >
          <div class="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mb-6 group-hover:bg-primary transition-colors duration-300">
            <font-awesome-icon 
              :icon="['fas', 'warehouse']" 
              class="text-2xl text-primary group-hover:text-white transition-colors duration-300"
            />
          </div>
          <h3 class="text-xl font-bold mb-4 text-tech-gray">仓库管理系统</h3>
          <p class="text-gray-600 mb-6">先进的仓库管理系统，优化库存，简化操作，提高准确性。</p>
          <a href="#" class="text-primary font-medium inline-flex items-center group-hover:text-tech-gray transition-colors">
            了解更多 
            <font-awesome-icon :icon="['fas', 'arrow-right']" class="ml-2 transition-transform duration-300 group-hover:translate-x-2" />
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { directive as vAnimate } from '../../directives/animate';
</script> 
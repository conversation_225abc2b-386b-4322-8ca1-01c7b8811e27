<template>
  <section id="home" class="pt-24 pb-16 md:pt-28 md:pb-20 bg-gradient-to-br from-gray-100 to-white relative overflow-hidden">
    <div class="container relative z-10">
      <div class="grid lg:grid-cols-2 gap-12 items-start">
        <div class="hero-content" v-animate>
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6 text-tech-gray">
            用<span class="relative inline-block text-primary">先进自动化
              <span class="absolute bottom-1 left-0 w-full h-3 bg-primary bg-opacity-20 -z-10"></span>
            </span>改变您的行业
          </h1>
          <p class="text-lg md:text-xl text-gray-600 mb-8">
            提供工业自动化、机器人、智能仓储、智能物流解决方案的专业解决方案，推动效率提升与创新发展。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="#" class="btn-primary">
              立即开始
              <font-awesome-icon :icon="['fas', 'arrow-right']" class="ml-2" />
            </a>
            <a href="#solutions" class="btn-primary">
              我们的解决方案
              <font-awesome-icon :icon="['fas', 'arrow-right']" class="ml-2" />
            </a>
          </div>
        </div>
        
        <div class="company-intro bg-blue-50 rounded-xl shadow-xl p-8 transform transition-all duration-500 hover:shadow-2xl border border-blue-100" v-animate>
          <h2 class="text-2xl font-bold mb-4 pb-3 relative">
            公司简介
            <span class="absolute bottom-0 left-0 w-20 h-1 bg-primary"></span>
          </h2>
          <p class="text-gray-600 leading-relaxed">
            上海奥图威智能科技有限公司位于上海市浦东新区，是一家提供集自动化机器人、视觉工业检测、智慧仓储等软硬件方案的高科技企业，专注于智能制造领域，以快速满足客户个性化、智能化生产需求的模式，实现企业价值与客户价值共同成长。
          </p>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="absolute top-0 right-0 w-1/3 h-1/3 bg-blue-50 rounded-full filter blur-3xl opacity-70 transform translate-x-1/2 -translate-y-1/2"></div>
    <div class="absolute bottom-0 left-0 w-1/4 h-1/4 bg-blue-100 rounded-full filter blur-3xl opacity-50 transform -translate-x-1/2 translate-y-1/2"></div>
  </section>
</template>

<script setup>
import { directive as vAnimate } from '../../directives/animate';
</script> 
#!/bin/bash

# 部署状态检查脚本
# 用于检查服务器部署状态和网站健康状况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

ECS_IP=${1:-"your-ecs-ip"}
USERNAME=${2:-"root"}

echo -e "${BLUE}🔍 检查部署状态...${NC}"

if [ "$ECS_IP" = "your-ecs-ip" ]; then
    echo -e "${RED}❌ 请提供 ECS IP 地址${NC}"
    echo -e "${YELLOW}使用方法: ./check-deployment.sh [ECS_IP] [USERNAME]${NC}"
    exit 1
fi

# 创建远程检查脚本
cat > remote-check.sh << 'EOF'
#!/bin/bash

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 系统信息 ===${NC}"
echo "操作系统: $(cat /etc/redhat-release)"
echo "内核版本: $(uname -r)"
echo "系统时间: $(date)"
echo

echo -e "${BLUE}=== 服务状态 ===${NC}"
if systemctl is-active --quiet nginx; then
    echo -e "Nginx: ${GREEN}运行中${NC}"
else
    echo -e "Nginx: ${RED}未运行${NC}"
fi

if systemctl is-enabled --quiet nginx; then
    echo -e "Nginx 开机启动: ${GREEN}已启用${NC}"
else
    echo -e "Nginx 开机启动: ${RED}未启用${NC}"
fi
echo

echo -e "${BLUE}=== 端口监听 ===${NC}"
netstat -tlnp | grep :80 || echo -e "${RED}端口 80 未监听${NC}"
echo

echo -e "${BLUE}=== 网站文件 ===${NC}"
if [ -d "/var/www/atv-website" ]; then
    echo -e "网站目录: ${GREEN}存在${NC}"
    echo "文件数量: $(find /var/www/atv-website -type f | wc -l)"
    echo "目录大小: $(du -sh /var/www/atv-website | cut -f1)"
    echo "最后修改: $(stat -c %y /var/www/atv-website/index.html 2>/dev/null || echo '未知')"
else
    echo -e "网站目录: ${RED}不存在${NC}"
fi
echo

echo -e "${BLUE}=== Nginx 配置 ===${NC}"
if [ -f "/etc/nginx/conf.d/atv-website.conf" ]; then
    echo -e "配置文件: ${GREEN}存在${NC}"
    nginx -t 2>&1 | head -2
else
    echo -e "配置文件: ${RED}不存在${NC}"
fi
echo

echo -e "${BLUE}=== 防火墙状态 ===${NC}"
if systemctl is-active --quiet firewalld; then
    echo -e "防火墙: ${GREEN}运行中${NC}"
    firewall-cmd --list-services | grep -q http && echo -e "HTTP 服务: ${GREEN}已开放${NC}" || echo -e "HTTP 服务: ${RED}未开放${NC}"
else
    echo -e "防火墙: ${YELLOW}未运行${NC}"
fi
echo

echo -e "${BLUE}=== 系统资源 ===${NC}"
echo "CPU 使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo "磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
echo

echo -e "${BLUE}=== 最近日志 ===${NC}"
if [ -f "/var/log/nginx/atv-website/error.log" ]; then
    echo "最近错误日志:"
    tail -5 /var/log/nginx/atv-website/error.log 2>/dev/null || echo "无错误日志"
else
    echo "错误日志文件不存在"
fi
EOF

# 上传并执行检查脚本
scp remote-check.sh ${USERNAME}@${ECS_IP}:/tmp/
ssh ${USERNAME}@${ECS_IP} "chmod +x /tmp/remote-check.sh && /tmp/remote-check.sh && rm -f /tmp/remote-check.sh"

# 本地网络测试
echo -e "${BLUE}=== 网络连通性测试 ===${NC}"
if curl -s --connect-timeout 5 http://${ECS_IP} > /dev/null; then
    echo -e "HTTP 访问: ${GREEN}正常${NC}"
    
    # 获取响应头信息
    echo -e "${BLUE}=== 响应信息 ===${NC}"
    curl -I http://${ECS_IP} 2>/dev/null | head -5
    
    # 检查页面内容
    if curl -s http://${ECS_IP} | grep -q "奥图威智能"; then
        echo -e "页面内容: ${GREEN}正确${NC}"
    else
        echo -e "页面内容: ${YELLOW}可能有问题${NC}"
    fi
else
    echo -e "HTTP 访问: ${RED}失败${NC}"
    echo -e "${YELLOW}请检查:${NC}"
    echo "1. ECS 安全组是否开放 80 端口"
    echo "2. 服务器防火墙设置"
    echo "3. Nginx 服务状态"
fi

rm -f remote-check.sh

echo -e "${GREEN}✅ 检查完成${NC}"

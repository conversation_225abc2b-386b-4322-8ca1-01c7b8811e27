#!/bin/bash

# 服务器端部署脚本 - 在 CentOS 7.9 上运行
# 此脚本将在 ECS 实例上执行

set -e

PROJECT_NAME=${1:-"atv-website"}
REMOTE_DIR="/var/www/${PROJECT_NAME}"
NGINX_CONF="/etc/nginx/conf.d/${PROJECT_NAME}.conf"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 开始服务器端部署配置...${NC}"

# 1. 更新系统
echo -e "${YELLOW}📦 更新系统包...${NC}"
yum update -y

# 2. 安装 EPEL 仓库
echo -e "${YELLOW}📦 安装 EPEL 仓库...${NC}"
yum install -y epel-release

# 3. 安装 Nginx
echo -e "${YELLOW}🌐 安装 Nginx...${NC}"
yum install -y nginx

# 4. 启动并启用 Nginx
echo -e "${YELLOW}🚀 启动 Nginx 服务...${NC}"
systemctl start nginx
systemctl enable nginx

# 5. 配置防火墙
echo -e "${YELLOW}🔥 配置防火墙...${NC}"
if systemctl is-active --quiet firewalld; then
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --reload
    echo -e "${GREEN}✅ 防火墙配置完成${NC}"
else
    echo -e "${YELLOW}⚠️  防火墙未运行，跳过配置${NC}"
fi

# 6. 创建项目目录
echo -e "${YELLOW}📁 创建项目目录...${NC}"
mkdir -p ${REMOTE_DIR}
mkdir -p /var/log/nginx/${PROJECT_NAME}

# 7. 解压项目文件
echo -e "${YELLOW}📦 解压项目文件...${NC}"
cd ${REMOTE_DIR}
tar -xzf /tmp/${PROJECT_NAME}-dist.tar.gz

# 8. 设置文件权限
echo -e "${YELLOW}🔐 设置文件权限...${NC}"
chown -R nginx:nginx ${REMOTE_DIR}
chmod -R 755 ${REMOTE_DIR}

# 9. 配置 Nginx
echo -e "${YELLOW}⚙️  配置 Nginx...${NC}"
cp /tmp/${PROJECT_NAME}.conf ${NGINX_CONF}

# 10. 测试 Nginx 配置
echo -e "${YELLOW}🧪 测试 Nginx 配置...${NC}"
nginx -t

# 11. 重新加载 Nginx
echo -e "${YELLOW}🔄 重新加载 Nginx...${NC}"
systemctl reload nginx

# 12. 清理临时文件
echo -e "${YELLOW}🧹 清理临时文件...${NC}"
rm -f /tmp/${PROJECT_NAME}-dist.tar.gz
rm -f /tmp/${PROJECT_NAME}.conf
rm -f /tmp/server-setup.sh

echo -e "${GREEN}✅ 服务器配置完成！${NC}"
echo -e "${BLUE}📊 Nginx 状态:${NC}"
systemctl status nginx --no-pager -l

echo -e "${BLUE}🌐 网站根目录: ${REMOTE_DIR}${NC}"
echo -e "${BLUE}📝 Nginx 配置: ${NGINX_CONF}${NC}"
echo -e "${BLUE}📋 Nginx 日志: /var/log/nginx/${PROJECT_NAME}/${NC}"
